{"name": "arrow-interview-assistant", "version": "1.0.0", "description": "Arrow ----------> A - AI-powered coding interview assistant", "main": "dist/main/main.js", "scripts": {"start": "npm run build && electron .", "build": "tsc && webpack --config webpack.config.js", "dev": "tsc && webpack --config webpack.config.js && electron .", "watch": "webpack --config webpack.config.js --watch", "dist": "npm run build && electron-builder", "dist:mac": "npm run build && electron-builder --mac", "dist:mac-universal": "npm run build && electron-builder --mac --universal", "pack": "npm run build && electron-builder --dir", "dist-win": "npm run build && electron-builder --win"}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@google/generative-ai": "^0.24.0", "@material-ui/core": "^4.12.4", "@types/prismjs": "^1.26.5", "@types/react-syntax-highlighter": "^15.5.13", "dotenv": "^10.0.0", "electron-is-dev": "^2.0.0", "electron-store": "^8.0.1", "node-fetch": "^2.7.0", "openai": "^4.0.0", "prismjs": "^1.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@types/node": "^18.11.9", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.9", "css-loader": "^7.1.2", "electron": "^21.3.1", "electron-builder": "^23.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^4.9.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "build": {"appId": "com.arrow.interview-assistant", "productName": "Arrow", "directories": {"output": "../Arrow_Distribution"}, "files": ["dist/**/*", "node_modules/**/*", "package.json", ".env"], "extraResources": [{"from": ".env", "to": ".env"}], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "dmg": {"title": "Arrow", "artifactName": "${productName}-${version}-${arch}.${ext}", "background": null, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}