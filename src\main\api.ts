import * as dotenv from 'dotenv';
import { getApiKey } from './embeddedConfig';

// Load environment variables
dotenv.config();

export function checkEnvironment() {
  return {
    hasGeminiKey: !!(process.env.GEMINI_API_KEY || getApiKey('GEMINI_API_KEY')),
    hasOpenAIKey: !!(process.env.OPENAI_API_KEY || getApiKey('OPENAI_API_KEY')),
    hasClaudeKey: !!(process.env.ANTHROPIC_API_KEY || getApiKey('ANTHROPIC_API_KEY'))
  };
}

export function initializeAPIs() {
  return {
    isGeminiInitialized: !!(process.env.GEMINI_API_KEY || getApiKey('GEMINI_API_KEY')),
    isOpenAIInitialized: !!(process.env.OPENAI_API_KEY || getApiKey('OPENAI_API_KEY')),
    isClaudeInitialized: !!(process.env.ANTHROPIC_API_KEY || getApiKey('ANTHROPIC_API_KEY'))
  };
}