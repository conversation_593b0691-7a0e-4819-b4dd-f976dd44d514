/**
 * Embedded configuration for the application
 * This file contains hardcoded API keys and other configuration
 * that will be bundled with the executable
 */

// Embedded API keys
export const EMBEDDED_CONFIG = {
  // Gemini API key
  GEMINI_API_KEY: 'AIzaSyAsvm9n014NDpKewRiEFidtSvvYePGHFWI',
  
  // OpenAI API key - uncomment and add if needed
  // OPENAI_API_KEY: '',
  
  // Claude API key - uncomment and add if needed
  // ANTHROPIC_API_KEY: '',
};

/**
 * Gets the API key for the specified service
 * First tries to get from environment variables, then falls back to embedded config
 * @param keyName The name of the API key to get
 * @returns The API key value or undefined if not found
 */
export function getApiKey(keyName: string): string | undefined {
  // First try to get from environment variables (for development)
  const envValue = process.env[keyName];
  
  // If not found in environment, use embedded config
  if (!envValue) {
    return EMBEDDED_CONFIG[keyName as keyof typeof EMBEDDED_CONFIG];
  }
  
  return envValue;
}
