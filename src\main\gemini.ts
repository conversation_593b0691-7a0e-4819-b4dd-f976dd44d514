import { GoogleGenerativeAI } from '@google/generative-ai';
import { getApi<PERSON>ey } from './embeddedConfig';

interface AnalysisResponse {
  solution: string;
  usage?: any;
  model: string;
}

const apiKey = process.env.GEMINI_API_KEY || getApiKey('GEMINI_API_KEY') || '';
const genAI = new GoogleGenerativeAI(apiKey);

export async function analyzeWithGemini(screenshots: string[], prompt: string): Promise<AnalysisResponse> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    const result = await model.generateContent([
      prompt || "Analyze this coding problem and provide a solution with detailed explanations. First provide a brute force solution with code and explanation for each line, then provide an optimized solution with code and explanation for each line.",
      ...screenshots.map(screenshot => ({
        inlineData: {
          mimeType: "image/jpeg" as const,
          data: screenshot
        }
      }))
    ]);

    const response = await result.response;
    return { 
      solution: response.text(),
      model: "gemini-1.5-pro"
    };
  } catch (error) {
    console.error('Gemini API error:', error);
    throw error;
  }
} 