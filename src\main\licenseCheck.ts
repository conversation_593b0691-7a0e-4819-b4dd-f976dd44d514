import { app, dialog } from 'electron';

/**
 * License expiration date: June 30th, 2025
 * After this date, the application will be disabled
 */
const EXPIRATION_DATE = new Date('2025-06-30T23:59:59');

/**
 * Checks if the application license has expired
 * @returns {boolean} True if the license is valid, false if expired
 */
export function isLicenseValid(): boolean {
  const currentDate = new Date();
  return currentDate < EXPIRATION_DATE;
}

/**
 * Shows an expiration message and quits the application
 */
export function showExpirationMessage(): void {
  dialog.showMessageBoxSync({
    type: 'error',
    title: 'Application Expired',
    message: 'This application has expired.',
    detail: 'Please contact the developer for a new version.',
    buttons: ['OK']
  });
  
  app.quit();
}

/**
 * Checks the license and shows expiration message if needed
 * @returns {boolean} True if the license is valid, false if expired
 */
export function checkLicense(): boolean {
  const isValid = isLicenseValid();
  
  if (!isValid) {
    showExpirationMessage();
  }
  
  return isValid;
}
