import 'dotenv/config';
import { app, BrowserWindow, ipcMain, screen } from 'electron';
import * as path from 'path';
import { registerShortcuts } from './shortcuts';
import { WindowManager } from './windowManager';
import { captureScreenshot } from './screenshot';
import {
  generateGeminiResponse,
  generateClaudeResponse,
  generateOpenAIResponse,
  analyzeWithGemini,
  analyzeWithClaude,
  analyzeWithOpenAI
} from './openai';
import isDev from 'electron-is-dev';
import Store from 'electron-store';
import { checkLicense } from './licenseCheck';
import { getApiKey, EMBEDDED_CONFIG } from './embeddedConfig';

// Initialize store for app settings
const store = new Store();

let windowManager: WindowManager;

// Set API keys from embedded config to process.env
process.env.GEMINI_API_KEY = getApiKey('GEMINI_API_KEY') || '';
process.env.OPENAI_API_KEY = getApiKey('OPENAI_API_KEY') || '';
process.env.ANTHROPIC_API_KEY = getApiKey('ANTHROPIC_API_KEY') || '';

app.whenReady().then(() => {
  // Check if license is valid
  if (!checkLicense()) {
    return; // Exit if license is expired
  }

  // Create window manager
  windowManager = new WindowManager();

  // Create main window
  const mainWindow = windowManager.createMainWindow();

  // Register all global shortcuts
  registerShortcuts(windowManager);

  // IPC handlers
  ipcMain.handle('take-screenshot', async () => {
    const screenshotData = await captureScreenshot();
    return screenshotData;
  });

  ipcMain.handle('analyze-code', async (_event, params) => {
    const { screenshots, model, prompt } = params;
    console.log('Using model for code analysis:', model);

    if (model.startsWith('gemini')) {
      if (!process.env.GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }
      return await analyzeWithGemini(screenshots, prompt, model);
    } else if (model.startsWith('claude')) {
      if (!process.env.ANTHROPIC_API_KEY) {
        throw new Error('Claude API key not configured');
      }
      return await analyzeWithClaude(screenshots, prompt);
    } else if (model.startsWith('gpt')) {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }
      return await analyzeWithOpenAI(screenshots, prompt);
    }

    throw new Error('Unsupported model selected');
  });

  ipcMain.handle('generate-response', async (_event, prompt: string, model: string) => {
    console.log('Using model:', model);

    if (model.startsWith('gemini')) {
      if (!process.env.GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }
      return await generateGeminiResponse(prompt, model);
    } else if (model.startsWith('claude')) {
      if (!process.env.ANTHROPIC_API_KEY) {
        throw new Error('Claude API key not configured');
      }
      return await generateClaudeResponse(prompt);
    } else if (model.startsWith('gpt')) {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }
      return await generateOpenAIResponse(prompt);
    }

    throw new Error('Unsupported model selected');
  });

  ipcMain.handle('move-window', (_, direction: string) => {
    windowManager.moveActiveWindow(direction);
    return true;
  });

  ipcMain.handle('toggle-visibility', () => {
    windowManager.toggleVisibility();
    return windowManager.isVisible();
  });

  ipcMain.handle('reset-session', () => {
    mainWindow.webContents.send('reset-ui');
    return true;
  });

  ipcMain.handle('check-api-status', async () => {
    return {
      hasGeminiKey: !!process.env.GEMINI_API_KEY,
      hasClaudeKey: !!process.env.ANTHROPIC_API_KEY,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY
    };
  });

  ipcMain.handle('resize-window', async (_, size: { width: number, height: number }) => {
    const mainWindow = windowManager.getMainWindow();
    if (mainWindow) {
      const { width, height } = size;
      mainWindow.setSize(width, height);
      mainWindow.center();
    }
  });

  ipcMain.handle('hide-window', () => {
    const mainWindow = windowManager.getMainWindow();
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      windowManager.createMainWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle app activation (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    windowManager.createMainWindow();
  }
});