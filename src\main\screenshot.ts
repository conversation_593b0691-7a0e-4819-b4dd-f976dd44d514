import { desktopCapturer, screen } from 'electron';

export async function captureScreenshot(): Promise<string> {
  try {
    // Get the primary display
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.size;
    
    // Capture the entire screen
    const sources = await desktopCapturer.getSources({
      types: ['screen'],
      thumbnailSize: { width, height }
    });
    
    // Get the main screen source
    const mainSource = sources.find(source => 
      source.name === 'Entire Screen' || 
      source.name === 'Screen 1' ||
      source.id.includes('screen')
    );
    
    if (!mainSource || !mainSource.thumbnail) {
      throw new Error('Could not capture screen');
    }
    
    // Convert the thumbnail to a data URL
    return mainSource.thumbnail.toDataURL();
  } catch (error) {
    console.error('Screenshot error:', error);
    throw error;
  }
} 