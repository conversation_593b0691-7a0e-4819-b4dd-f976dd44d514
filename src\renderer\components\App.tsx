import React, { useState, useEffect } from 'react';
import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import Prism from 'prismjs';
import 'prismjs/themes/prism-tomorrow.css';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-python';
import '../vscode-dark.css';
import '../styles.css';
import ModelSelector from './ModelSelector';

interface Screenshot {
  id: string;
  data: string;
  timestamp: number;
}

const App: React.FC = () => {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([]);
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [userPrompt, setUserPrompt] = useState('');
  const [outputSize, setOutputSize] = useState<'default' | 'mid' | 'large'>('default');

  const takeScreenshot = async () => {
    console.log('Taking screenshot...');
    try {
      const imageData = await ipcRenderer.invoke('take-screenshot');
      console.log('Screenshot response:', imageData ? 'received' : 'empty');
      if (imageData) {
        const base64Data = imageData.startsWith('data:image') ? imageData : `data:image/jpeg;base64,${imageData}`;
        const newScreenshot: Screenshot = {
          id: Date.now().toString(),
          data: base64Data,
          timestamp: Date.now()
        };
        setScreenshots(prev => [...prev, newScreenshot]);
        setError('');
        console.log('Screenshot added successfully');
      } else {
        setError('No image data received from screenshot');
        console.error('Screenshot failed: No image data received');
      }
    } catch (err) {
      setError('Failed to capture screenshot');
      console.error('Screenshot error:', err);
    }
  };

  const clearScreenshots = () => {
    setScreenshots([]);
    setAnalysis('');
    setError('');
    setUserPrompt('');
  };

  const removeScreenshot = (id: string) => {
    setScreenshots(prev => prev.filter(screenshot => screenshot.id !== id));
  };

  const generateFromScreenshots = async () => {
    if (screenshots.length === 0) {
      setError('Please take at least one screenshot first');
      return;
    }

    setIsAnalyzing(true);
    setError('');
    setAnalysis('');

    try {
      console.log('Analyzing screenshots...');
      const result = await ipcRenderer.invoke('analyze-code', {
        screenshots: screenshots.map(s => s.data),
        model: selectedModel,
        prompt: "Analyze this coding problem and provide a solution with detailed explanations. First provide a brute force solution with code and explanation for each line, then provide an optimized solution with code and explanation for each line."
      });
      console.log('Analysis completed successfully');
      if (result && result.solution) {
        setAnalysis(result.solution);
      } else {
        setError('No analysis generated');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to analyze code');
      console.error('Analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateFromPrompt = async () => {
    if (!userPrompt.trim() && screenshots.length === 0) {
      setError('Please enter a prompt or take a screenshot first');
      return;
    }

    setIsAnalyzing(true);
    setError('');
    setAnalysis('');

    try {
      console.log('Sending prompt analysis request...');
      const result = await ipcRenderer.invoke('analyze-code', {
        screenshots: screenshots.map(s => s.data),
        model: selectedModel,
        prompt: userPrompt || "Analyze this coding problem and provide a solution with detailed explanations."
      });
      console.log('Analysis completed successfully');
      if (result && result.solution) {
        setAnalysis(result.solution);
        Prism.highlightAll();
      } else {
        setError('No analysis generated');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to analyze code');
      console.error('Analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleWindowSize = async (size: 'default' | 'mid' | 'large') => {
    const sizes = {
      default: { width: 800, height: 600 },
      mid: { width: 1024, height: 768 },
      large: { width: 1280, height: 960 }
    };
    await ipcRenderer.invoke('resize-window', sizes[size]);
  };

  const handleModelChange = (model: string) => {
    console.log('Model changed to:', model);
    setSelectedModel(model);
  };

  useEffect(() => {
    const handleKeyPress = async (event: KeyboardEvent) => {
      if (!isAnalyzing) {
        if (event.metaKey || event.ctrlKey) {
          switch (event.key.toLowerCase()) {
            case 'enter':
              event.preventDefault();
              if (userPrompt.trim() || screenshots.length > 0) {
                await generateFromPrompt();
              }
              break;
            case 'g':
              event.preventDefault();
              if (userPrompt.trim() || screenshots.length > 0) {
                await generateFromPrompt();
              }
              break;
            case 'h':
              event.preventDefault();
              await takeScreenshot();
              break;
            case 'r':
              event.preventDefault();
              clearScreenshots();
              break;
            case 'b':
              event.preventDefault();
              await ipcRenderer.invoke('toggle-visibility');
              break;
            case 'q':
              event.preventDefault();
              ipcRenderer.invoke('quit-app');
              break;
            case 'arrowleft':
            case 'arrowright':
            case 'arrowup':
            case 'arrowdown':
              event.preventDefault();
              ipcRenderer.invoke('move-window', event.key.replace('arrow', '').toLowerCase());
              break;
            case '1':
              event.preventDefault();
              await handleWindowSize('default');
              break;
            case '2':
              event.preventDefault();
              await handleWindowSize('mid');
              break;
            case '3':
              event.preventDefault();
              await handleWindowSize('large');
              break;
          }
        }
      }
    };

    // Add IPC listeners
    ipcRenderer.on('take-screenshot', () => {
      if (!isAnalyzing) {
        takeScreenshot();
      }
    });

    ipcRenderer.on('reset-app', () => {
      if (!isAnalyzing) {
        clearScreenshots();
      }
    });

    ipcRenderer.on('generate-analysis', () => {
      if (!isAnalyzing && (userPrompt.trim() || screenshots.length > 0)) {
        generateFromPrompt();
      }
    });

    ipcRenderer.on('toggle-visibility', (_, isVisible) => {
      // You can add UI feedback here if needed
      console.log('Window visibility:', isVisible ? 'visible' : 'hidden');
    });

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
      ipcRenderer.removeAllListeners('take-screenshot');
      ipcRenderer.removeAllListeners('reset-app');
      ipcRenderer.removeAllListeners('generate-analysis');
      ipcRenderer.removeAllListeners('toggle-visibility');
    };
  }, [isAnalyzing, userPrompt, screenshots.length]);

  useEffect(() => {
    if (analysis) {
      Prism.highlightAll();
    }
  }, [analysis]);

  // Add function to determine button color
  const getGenerateButtonClass = () => {
    const baseClass = "control-button generate-button";
    if (isAnalyzing) return `${baseClass} disabled`;
    if (userPrompt.trim() || screenshots.length > 0) return `${baseClass} ready`;
    return baseClass;
  };

  const getOutputStyle = () => {
    switch (outputSize) {
      case 'mid':
        return { width: '100%', fontSize: '1.1em' };
      case 'large':
        return { width: '100%', fontSize: '1.2em' };
      default:
        return { width: '100%', fontSize: '1em' };
    }
  };

  return (
    <div className="app">
      <div className="shortcuts-bar">
        <span>⌘ + H: Take screenshot of the coding problem</span>
        <span>⌘ + B: Toggle window visibility</span>
        <span>⌘ + Arrow keys: Move window position</span>
        <span>⌘ + Enter: Generate solution</span>
        <span>⌘ + R: Reset session</span>
        <span>⌘ + Q: Quit application</span>
        <span>⌘ + 1: Default size</span>
        <span>⌘ + 2: Mid Size</span>
        <span>⌘ + 3: Big Size</span>
      </div>

      <div className="app-title">
        <h2>Arrow ----------&gt; A</h2>
      </div>

      <ModelSelector onModelChange={handleModelChange} />

      <div className="button-container">
        <button
          className={getGenerateButtonClass()}
          onClick={generateFromPrompt}
          disabled={isAnalyzing || (!userPrompt.trim() && screenshots.length === 0)}
        >
          Generate (⌘ + Enter)
        </button>
        <button
          className="control-button screenshot-button"
          onClick={takeScreenshot}
          disabled={isAnalyzing}
        >
          Take Screenshot (⌘ + H)
        </button>
        <button
          className="control-button reset-button"
          onClick={clearScreenshots}
          disabled={isAnalyzing}
        >
          Reset (⌘ + R)
        </button>
      </div>

      <div className="prompt-container">
        <textarea
          className="prompt-input"
          value={userPrompt}
          onChange={(e) => setUserPrompt(e.target.value)}
          placeholder="Enter your prompt here..."
          disabled={isAnalyzing}
        />
      </div>

      {screenshots.length > 0 && (
        <div className="screenshot-container">
          <div className="screenshots-grid">
            {screenshots.map((screenshot, index) => (
              <div key={screenshot.id} className="screenshot-item">
                <div className="screenshot-header">
                  <span>Part {index + 1}</span>
                  <button
                    onClick={() => removeScreenshot(screenshot.id)}
                    className="remove-screenshot"
                    title="Remove screenshot"
                  >
                    ×
                  </button>
                </div>
                <img
                  src={screenshot.data}
                  alt={`Screenshot ${index + 1}`}
                  className="screenshot-image"
                  onError={(e) => {
                    console.error('Image failed to load:', screenshot.id);
                    (e.target as HTMLImageElement).style.display = 'none';
                    setError(`Failed to load screenshot ${index + 1}`);
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="analysis-container">
        {isAnalyzing ? (
          <div className="loading">
            <div className="spinner"></div>
            <p>Analyzing...</p>
          </div>
        ) : analysis ? (
          <div className="analysis-result" style={getOutputStyle()}>
            <pre>
              <code className="language-typescript">
                {analysis}
              </code>
            </pre>
          </div>
        ) : null}
        {error && <div className="error">{error}</div>}
      </div>
    </div>
  );
};

export default App;